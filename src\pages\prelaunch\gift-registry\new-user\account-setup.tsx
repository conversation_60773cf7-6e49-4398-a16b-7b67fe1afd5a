/* eslint-disable @typescript-eslint/no-explicit-any */
import { ArrowRight, Bank, CloseCircle, Moneys } from 'iconsax-react';
import { useState, useEffect, useCallback, useRef } from 'react';
import { TextInput } from '../../../../components/inputs/text-input/text-input';
import { useQuery, useMutation } from '@tanstack/react-query';
import { GiftRegistryServices } from '../../../../lib/services/gift-registry';

interface AccountDetailsProps {
  onNextStep: (data: { bank: string; accountNumber: string }) => void;
  initialData?: { bank?: string; accountNumber?: string };
}

interface Bank {
  bank_code: string;
  bank_name: string;
}

interface ValidationResult {
  account_name: string;
  account_number: string;
  bank_code: string;
}

export const AccountSetup = ({
  onNextStep,
  initialData = {},
}: AccountDetailsProps) => {
  const [bank, setBank] = useState(initialData.bank || '');
  const [accountNumber, setAccountNumber] = useState(
    initialData.accountNumber || ''
  );
  const [validationResult, setValidationResult] =
    useState<ValidationResult | null>(null);
  const [validationError, setValidationError] = useState<string>('');
  const [saveError, setSaveError] = useState<string>('');
  const validationTimeoutRef = useRef<NodeJS.Timeout | null>(null);

  const {
    data: banks,
    isLoading,
    error,
  } = useQuery({
    queryKey: ['payoutBanks'],
    queryFn: GiftRegistryServices.getPayoutBanks,
  });

  const validationMutation = useMutation({
    mutationFn: (payload: { bank_code: string; account_number: string }) =>
      GiftRegistryServices.resolveUserPayoutBank(payload),
    onSuccess: (data) => {
      setValidationResult(data.data);
      setValidationError('');
    },
    onError: () => {
      setValidationResult(null);
      setValidationError('Invalid account details');
    },
  });

  const createBankMutation = useMutation({
    mutationFn: (payload: { bank_code: string; account_number: string }) =>
      GiftRegistryServices.createUserPayoutBank(payload),
    onSuccess: () => {
      onNextStep({
        bank,
        accountNumber,
      });
    },
    onError: (error: any) => {
      setSaveError(error?.response?.data?.message || 'Failed to save bank details. Please try again.');
    },
  });

  useEffect(() => {
    document.body.style.overflow = 'hidden';
    return () => {
      document.body.style.overflow = '';
    };
  }, []);

  const validateAccount = useCallback(
    (bankCode: string, accNumber: string) => {
      if (validationTimeoutRef.current) {
        clearTimeout(validationTimeoutRef.current);
      }

      setValidationResult(null);
      setValidationError('');
      validationTimeoutRef.current = setTimeout(() => {
        validationMutation.mutate({
          bank_code: bankCode,
          account_number: accNumber,
        });
      }, 500);
    },
    [validationMutation.mutate]
  );

  useEffect(() => {
    if (bank && accountNumber && accountNumber.length >= 10) {
      validateAccount(bank, accountNumber);
    } else {
      setValidationResult(null);
      setValidationError('');
      if (validationTimeoutRef.current) {
        clearTimeout(validationTimeoutRef.current);
      }
    }

    return () => {
      if (validationTimeoutRef.current) {
        clearTimeout(validationTimeoutRef.current);
      }
    };
  }, [bank, accountNumber, validateAccount]);

  const handleContinue = () => {
    if (validationResult) {
      setSaveError('');
      createBankMutation.mutate({
        bank_code: bank,
        account_number: accountNumber,
      });
    }
  };

  const isFormValid =
    bank && accountNumber && validationResult && !validationMutation.isPending && !createBankMutation.isPending;

  return (
    <div className="px-4 md:px-0 md:ml-3.5">
      <div className="max-w-[550px] mx-auto mb-32 mt-9">
        <h2 className="md:text-[40px] text-2xl font-medium -tracking-[0.03em]">
          Add Account details
        </h2>
        <div className="bg-white rounded-[20px] mt-20 px-5 pb-6 w-full">
          <div className="w-26 h-26 -translate-y-15 border-[9px] border-white rounded-full bg-cus-pink-900 flex items-center justify-center cursor-pointer relative overflow-hidden">
            <input
              type="file"
              accept="image/*"
              className="absolute inset-0 opacity-0 cursor-pointer z-10"
            />

            <Moneys variant="Bulk" size="62" color="#FFAA8C" />
          </div>

          <div className="-mt-12">
            <div className="bg-cus-pink flex items-center gap-3 p-3.5 mb-6 rounded-2xl">
              <Bank color="#F7BFA9" size={56} variant="Bulk" />
              <p className="italic text-dark-blue-500 font-medium text-xs md:text-sm">
                Add your bank details so guests can easily contribute cash if
                <br /> they prefer to gift money instead of purchasing an item.
              </p>
            </div>
            <div className="mb-6">
              <label className="block text-grey-500 font-medium text-sm mb-2">
                Bank
              </label>
              <select
                value={bank}
                onChange={(e) => setBank(e.target.value)}
                className="w-full h-[44px] px-3.5 border border-gray-300 rounded-full text-base font-bold text-grey-50 italic outline-0"
                disabled={isLoading}>
                <option value="" disabled>
                  {isLoading ? 'Loading banks...' : 'Select a bank'}
                </option>
                {error ? (
                  <option value="" disabled>
                    Error loading banks
                  </option>
                ) : (
                  banks?.data?.map((bank: Bank) => (
                    <option key={bank?.bank_code} value={bank?.bank_code}>
                      {bank?.bank_name}
                    </option>
                  ))
                )}
              </select>
              {error && (
                <span className="text-xs italic text-red-500">
                  Failed to load banks. Please try again.
                </span>
              )}
            </div>
            <div className="mb-6">
              <TextInput
                id="accountNumber"
                label="Account Number"
                value={accountNumber}
                onChange={(e) => setAccountNumber(e.target.value)}
                placeholder="e.g **********"
                className="text-grey-50 font-bold italic placeholder:font-normal placeholder:text-grey-700"
              />
              <div className="mt-1">
                {validationMutation.isPending && bank && accountNumber && (
                  <span className="text-xs italic text-grin-100">
                    Validating account...
                  </span>
                )}
                {validationResult && (
                  <span className="text-sm flex items-center w-fit gap-2 rounded-full mt-2 font-bold italic text-grin-100 bg-grin py-1 px-2.5">
                    <svg
                      width="14"
                      height="14"
                      viewBox="0 0 14 14"
                      fill="none"
                      xmlns="http://www.w3.org/2000/svg">
                      <rect
                        x="1"
                        y="1"
                        width="12"
                        height="12"
                        rx="6"
                        fill="#3CC35C"
                      />
                      <rect
                        x="1"
                        y="1"
                        width="12"
                        height="12"
                        rx="6"
                        stroke="#3CC35C"
                        stroke-width="2"
                      />
                      <path
                        d="M9.18395 5.36328L6.18395 8.36328L4.82031 6.99964"
                        stroke="white"
                        stroke-width="1.4"
                        stroke-linecap="round"
                        stroke-linejoin="round"
                      />
                    </svg>

                    {validationResult.account_name}
                  </span>
                )}
                {validationError && (
                  <span className="text-sm font-medium italic text-cus-red-100 rounded-full w-fit bg-cus-pink-250 flex items-center gap-2 py-2 px-4">
                    <CloseCircle size={14} color="#B20000" variant="Bulk" />
                    {validationError}
                  </span>
                )}

                {!validationMutation.isPending &&
                  !validationResult &&
                  !validationError && (
                    <span className="text-xs italic text-grey-650">
                      Enter account number to receive cash gift
                    </span>
                  )}
                {saveError && (
                  <span className="text-sm font-medium italic text-cus-red-100 rounded-full w-fit bg-cus-pink-250 flex items-center gap-2 py-2 px-4 mt-2">
                    <CloseCircle size={14} color="#B20000" variant="Bulk" />
                    {saveError}
                  </span>
                )}
              </div>
            </div>
            <button
              onClick={handleContinue}
              disabled={!isFormValid}
              className={`bg-primary-650 text-white py-2.5 px-4 mb-0 rounded-full cursor-pointer flex items-center gap-2 ${
                isFormValid ? '' : 'opacity-50 cursor-not-allowed'
              }`}>
              {validationMutation.isPending
                ? 'Validating...'
                : createBankMutation.isPending
                ? 'Saving...'
                : isLoading
                ? 'Loading...'
                : 'Continue'}
              <div className="bg-white/30 rounded-full p-0.5">
                <ArrowRight size="12" color="#fff" />
              </div>
            </button>
          </div>
        </div>
      </div>
    </div>
  );
};
