// import { ArrowRight } from 'iconsax-react';
// import { Link } from 'react-router-dom';
// import emoji from '../../../assets/images/emoji.png';

// export const Success = () => {
//   return (
//     <div className=" md:w-[450px] bg-white rounded-[28px] fixed inset-0">
//       <div
//         className="bg-[linear-gradient(180deg,_#FDEFE9_0%,_#FEF7F4_100%)] relative rounded-t-[20px] h-[262px] w-full overflow-hidden"
//         style={{
//           clipPath: 'polygon(0 0, 100% 0, 100% 100%, 70% 95%, 30% 95%, 0 100%)',
//         }}>
//         {' '}
//         <div className="flex justify-center items-center text-[155px] h-full">
//           <img src={emoji} alt="emoji" />
//         </div>
//       </div>{' '}
//       <div className="relative z-10 flex flex-col items-center text-center py-12 px-4 w-full">
//         <h2 className="text-4xl font-medium mb-2 text-dark-200">
//           Your Password has <br />{' '}
//           <span className="text-[18px] md:text-[26px] text-grey-250">
//             been changed successfully
//           </span>
//         </h2>
//         <p className="text-grey-250 text-base mb-8">
//           Invites sent successfully! Your guests <br />
//           will receive them shortly.
//         </p>
//         <Link
//           to="/login"
//           className="bg-primary text-2xl text-white flex items-center py-3 px-6 font-semibold rounded-full gap-2 hover:bg-[#4A48E0] transition-colors">
//           <span>Continue to Sign In</span>
//           <div className="rounded-full bg-white/30 p-0.5">
//             <ArrowRight size="16" color="#fff" />
//           </div>
//         </Link>
//       </div>
//     </div>
//   );
// };

// import { useNavigate } from "react-router-dom";
import { motion } from 'framer-motion';
// import wed from '../../../assets/images/wed.png';
import cash from '../../../assets/images/cash-success.png';
import box from '../../../assets/images/small-box.png';
import { useState } from 'react';
import { useEventStore } from '../../../lib/store/event';
export const Success = ({
  onClose = () => {},
  cashCount = 0,
}: {
  onClose: () => void;
  cashCount?: number;
}) => {
  // const navigate = useNavigate();
  const { selectedEvent } = useEventStore();
  const [copied, setCopied] = useState(false);
  const registryLink = 'https://EventPark.com/event/inrd...';

  const copyToClipboard = () => {
    navigator.clipboard
      .writeText(registryLink)
      .then(() => {
        setCopied(true);
        setTimeout(() => setCopied(false), 2000);
      })
      .catch((err) => {
        console.error('Failed to copy: ', err);
      });
  };

  const goHome = () => {
    onClose();
  };
  return (
    <div className="fixed inset-0 z-50 overflow-y-auto">
      <div className="min-h-screen px-4 bg-[linear-gradient(177.78deg,_var(--color-cus-pink)_24.89%,_var(--color-primary-150)_98.13%)] flex items-center justify-center">
        <div className="relative w-full pt-20 md:pt-34 px-4 md:px-0 pb-20">
          <div
            className="absolute inset-0 h-[720px] md:h-[774px] top-0 bg-[url(/src/assets/animations/gift.gif)] opacity-40 z-0"
            style={{ backgroundSize: 'cover' }}
          />
          <motion.div
            initial={{ scale: 0.5, opacity: 0 }}
            animate={{ scale: 1, opacity: 1 }}
            transition={{ duration: 0.5 }}
            className="relative z-20 bg-white border-t border-white rounded-[20px] text-center max-w-[450px] w-full mx-auto shadow-[0px_12px_120px_0px_#5F5F5F0F] overflow-hidden">
            {/* Triangle background */}
            <div
              className="bg-[linear-gradient(180deg,_#FDEFE9_0%,_#FEF7F4_100%)] rounded-t-[20px] h-[262px] w-full overflow-hidden"
              style={{
                clipPath:
                  'polygon(0 0, 100% 0, 100% 100%, 70% 95%, 30% 95%, 0 100%)',
              }}
            />
            {/* For cash gift */}

            <div className="absolute left-1/2 top-[16%] -translate-x-1/2">
              <div className="relative h-[200px]">
                <img
                  src={box}
                  alt="decoration"
                  className="absolute -top-18 -right-7 w-30 h-30 z-0"
                />
                <div className="overflow-hidden border-4 border-white rounded-[17px] bg-[#FEF7F4] w-[161px] h-[166px] backdrop-blur-[17.52px] shadow-[0px_24.64px_24.64px_0px_#A6A6A60A,0px_49.28px_61.6px_0px_#A6A6A61A,0px_28.97px_144.87px_0px_#0000000F] rotate-[-5deg] z-10 relative">
                  <div>
                    <img src={cash} alt="cash" className="rotate-[4deg]" />
                    <p className="font-bold text-base w-full px-2 truncate text-left">
                      {selectedEvent?.title || ''}
                    </p>
                    <div className="text-left px-2 w-full text-xs text-gray-400">
                      {cashCount} Gift Items
                    </div>
                  </div>
                </div>
              </div>
            </div>

            {/* <div className="absolute top-10 sm:top-0 left-0 overflow-hidden">
              <img
                src={wed}
                alt="Wedding celebration"
                className="w-full h-full object-cover"
              />
            </div> */}

            <div className="flex flex-col items-center text-center py-12 px-4 w-full">
              <h2 className="text-[20px] md:text-4xl font-medium my-2 text-dark-200">
                Your Gift Registry has <br />{' '}
                <span className="text-[18px] md:text-[26px] text-grey-250">
                  been created Successfully
                </span>
              </h2>
              <p className="text-grey-250 text-[14px] md:text-base mt-4 mb-7.5 text-center max-w-[292px]">
                Invites sent successfully! Your guests will receive them
                shortly.
              </p>

              <div className="bg-[#EDEEFE] rounded-t-2xl p-4 w-full max-w-[450px] mt-2">
                <p className="text-gray-700 text-sm font-medium mb-3">
                  Copy this link to share your gift registry with family and
                  friends
                </p>
                <div className="flex items-center justify-between bg-white rounded-full p-2 pl-4 italic">
                  <span className="text-sm text-[#000059]  truncate mr-2">
                    <strong>Link:</strong> {registryLink}
                  </span>
                  <button
                    type="button"
                    onClick={copyToClipboard}
                    className="bg-primary-100 text-primary flex items-center gap-1 py-1.5 px-4 rounded-full">
                    <span className="text-sm font-medium">
                      {copied ? 'Copied!' : 'Copy'}
                    </span>
                    <svg
                      width="12"
                      height="12"
                      viewBox="0 0 12 12"
                      fill="none"
                      xmlns="http://www.w3.org/2000/svg">
                      <path
                        d="M8 6.45V8.55C8 10.3 7.3 11 5.55 11H3.45C1.7 11 1 10.3 1 8.55V6.45C1 4.7 1.7 4 3.45 4H5.55C7.3 4 8 4.7 8 6.45Z"
                        fill="#4D55F2"
                      />
                      <path
                        opacity="0.4"
                        d="M8.55281 1H6.45281C4.72781 1 4.02781 1.685 4.00781 3.375H5.55281C7.65281 3.375 8.62781 4.35 8.62781 6.45V7.995C10.3178 7.975 11.0028 7.275 11.0028 5.55V3.45C11.0028 1.7 10.3028 1 8.55281 1Z"
                        fill="#4D55F2"
                      />
                    </svg>
                  </button>
                </div>
              </div>
            </div>
          </motion.div>
          <div className="flex justify-center mt-10">
            <button
              type="button"
              onClick={goHome}
              className="underline text-cus-orange-100 italic font-bold text-lg cursor-pointer">
              Back to dashboard
            </button>
          </div>
        </div>
      </div>
    </div>
  );
};
